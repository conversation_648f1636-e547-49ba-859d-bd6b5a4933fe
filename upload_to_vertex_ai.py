#!/usr/bin/env python3
"""
Simple script to upload your train/test/val folders to Vertex AI
Just change the configuration variables below and run!
"""

import os
from pathlib import Path

# ============================================================================
# CONFIGURATION - CHANGE THESE VALUES FOR YOUR PROJECT
# ============================================================================

PROJECT_ID = "your-project-id"  # ← CHANGE THIS: Your Google Cloud project ID
BUCKET_NAME = "your-bucket-name"  # ← CHANGE THIS: GCS bucket name (will be created)
DATASET_NAME = "olive-palm-disease-dataset"  # ← CHANGE THIS: Name for your dataset
REGION = "us-central1"  # ← CHANGE THIS: Google Cloud region
CREDENTIALS_PATH = "service-account-key.json"  # ← CHANGE THIS: Path to JSON key file

# ============================================================================
# CODE - NO NEED TO CHANGE BELOW THIS LINE
# ============================================================================

try:
    from google.cloud import storage
    from google.cloud import aiplatform
    from google.oauth2 import service_account
except ImportError:
    print("Installing required packages...")
    os.system("pip install google-cloud-storage google-cloud-aiplatform")
    from google.cloud import storage
    from google.cloud import aiplatform
    from google.oauth2 import service_account

def upload_to_vertex_ai():
    """Main function to upload dataset to Vertex AI"""

    # Initialize Google Cloud clients
    if os.path.exists(CREDENTIALS_PATH):
        credentials = service_account.Credentials.from_service_account_file(CREDENTIALS_PATH)
        storage_client = storage.Client(credentials=credentials, project=PROJECT_ID)
        aiplatform.init(project=PROJECT_ID, location=REGION, credentials=credentials)
    else:
        storage_client = storage.Client(project=PROJECT_ID)
        aiplatform.init(project=PROJECT_ID, location=REGION)

    # Create bucket if it doesn't exist
    print(f"Setting up bucket: {BUCKET_NAME}")
    try:
        bucket = storage_client.bucket(BUCKET_NAME)
        if not bucket.exists():
            bucket = storage_client.create_bucket(BUCKET_NAME, location=REGION)
            print(f"✓ Created bucket: {BUCKET_NAME}")
        else:
            print(f"✓ Using existing bucket: {BUCKET_NAME}")
    except Exception as e:
        print(f"Error with bucket: {e}")
        return

    # Get class labels from train directory
    train_dir = Path("train")
    if not train_dir.exists():
        print("❌ Train directory not found!")
        return

    labels = []
    for item in train_dir.iterdir():
        if item.is_dir():
            labels.append(item.name)
    labels = sorted(labels)
    print(f"✓ Found {len(labels)} classes: {labels[:5]}..." if len(labels) > 5 else f"✓ Found classes: {labels}")

    # Upload files function
    def upload_folder(folder_name):
        """Upload a folder to GCS"""
        folder_path = Path(folder_name)
        if not folder_path.exists():
            print(f"⚠️  {folder_name} directory not found, skipping...")
            return 0

        uploaded_count = 0
        print(f"📁 Uploading {folder_name} folder...")

        for file_path in folder_path.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
                # Create GCS path
                relative_path = file_path.relative_to(folder_path)
                gcs_path = f"{DATASET_NAME}/{folder_name}/{relative_path}".replace("\\", "/")

                blob = bucket.blob(gcs_path)

                try:
                    blob.upload_from_filename(str(file_path))
                    uploaded_count += 1
                    if uploaded_count % 50 == 0:
                        print(f"   Uploaded {uploaded_count} files...")
                except Exception as e:
                    print(f"   Error uploading {file_path}: {e}")

        print(f"✓ Uploaded {uploaded_count} files from {folder_name}")
        return uploaded_count

    # Upload all folders
    total_uploaded = 0
    for folder in ["train", "test", "val"]:
        count = upload_folder(folder)
        total_uploaded += count

    # Create manifest file for Vertex AI
    print("📝 Creating manifest file...")
    manifest_lines = []

    for split in ["train", "test", "val"]:
        split_dir = Path(split)
        if not split_dir.exists():
            continue

        for label in labels:
            label_dir = split_dir / label
            if not label_dir.exists():
                continue

            for image_file in label_dir.glob("*"):
                if image_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
                    gcs_path = f"gs://{BUCKET_NAME}/{DATASET_NAME}/{split}/{label}/{image_file.name}"
                    manifest_lines.append(f"{gcs_path},{label}")

    # Save and upload manifest
    manifest_filename = f"{DATASET_NAME}_manifest.csv"
    with open(manifest_filename, 'w') as f:
        f.write("image_path,label\n")
        f.write("\n".join(manifest_lines))

    # Upload manifest to GCS
    manifest_blob = bucket.blob(f"{DATASET_NAME}/manifest.csv")
    manifest_blob.upload_from_filename(manifest_filename)
    manifest_gcs_path = f"gs://{BUCKET_NAME}/{DATASET_NAME}/manifest.csv"

    # Success message
    print("\n" + "="*60)
    print("🎉 UPLOAD COMPLETE!")
    print("="*60)
    print(f"📊 Total files uploaded: {total_uploaded}")
    print(f"🏷️  Number of classes: {len(labels)}")
    print(f"📁 Dataset location: gs://{BUCKET_NAME}/{DATASET_NAME}")
    print(f"📋 Manifest file: {manifest_gcs_path}")
    print("\n🚀 Next Steps:")
    print("1. Go to Google Cloud Console → Vertex AI → Datasets")
    print("2. Click 'Create Dataset' → 'Image Classification'")
    print("3. Import data using the manifest file above")
    print("4. Start training your model!")
    print("="*60)

if __name__ == "__main__":
    upload_to_vertex_ai()
