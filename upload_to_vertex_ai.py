#!/usr/bin/env python3
"""
<PERSON><PERSON>t to upload train/test/val folders to Google Cloud Storage and create a Vertex AI dataset.
This script handles the complete workflow for preparing data for Vertex AI model training.
"""

import os
import json
import zipfile
from pathlib import Path
from typing import Dict, List, Tuple
import argparse

try:
    from google.cloud import storage
    from google.cloud import aiplatform
    from google.oauth2 import service_account
except ImportError:
    print("Google Cloud libraries not installed. Please run:")
    print("pip install google-cloud-storage google-cloud-aiplatform")
    exit(1)


class VertexAIDatasetUploader:
    def __init__(self, project_id: str, region: str, bucket_name: str, credentials_path: str = None):
        """
        Initialize the uploader with Google Cloud project details.
        
        Args:
            project_id: Your Google Cloud project ID
            region: Region for Vertex AI (e.g., 'us-central1')
            bucket_name: GCS bucket name to store the dataset
            credentials_path: Path to service account JSON file (optional if using default credentials)
        """
        self.project_id = project_id
        self.region = region
        self.bucket_name = bucket_name
        
        # Initialize credentials
        if credentials_path and os.path.exists(credentials_path):
            credentials = service_account.Credentials.from_service_account_file(credentials_path)
            self.storage_client = storage.Client(credentials=credentials, project=project_id)
            aiplatform.init(project=project_id, location=region, credentials=credentials)
        else:
            self.storage_client = storage.Client(project=project_id)
            aiplatform.init(project=project_id, location=region)
    
    def create_bucket_if_not_exists(self):
        """Create GCS bucket if it doesn't exist."""
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            if not bucket.exists():
                bucket = self.storage_client.create_bucket(self.bucket_name, location=self.region)
                print(f"Created bucket: {self.bucket_name}")
            else:
                print(f"Bucket {self.bucket_name} already exists")
        except Exception as e:
            print(f"Error creating bucket: {e}")
            raise
    
    def get_class_labels(self, data_dir: str) -> List[str]:
        """Extract class labels from directory structure."""
        train_dir = Path(data_dir) / "train"
        if not train_dir.exists():
            raise ValueError(f"Train directory not found: {train_dir}")
        
        labels = []
        for item in train_dir.iterdir():
            if item.is_dir():
                labels.append(item.name)
        
        return sorted(labels)
    
    def upload_directory_to_gcs(self, local_dir: str, gcs_prefix: str) -> int:
        """
        Upload a directory to Google Cloud Storage.
        
        Args:
            local_dir: Local directory path
            gcs_prefix: GCS prefix (folder) to upload to
            
        Returns:
            Number of files uploaded
        """
        local_path = Path(local_dir)
        if not local_path.exists():
            print(f"Directory not found: {local_dir}")
            return 0
        
        bucket = self.storage_client.bucket(self.bucket_name)
        uploaded_count = 0
        
        for file_path in local_path.rglob("*"):
            if file_path.is_file():
                # Create relative path for GCS
                relative_path = file_path.relative_to(local_path)
                gcs_path = f"{gcs_prefix}/{relative_path}".replace("\\", "/")
                
                blob = bucket.blob(gcs_path)
                
                # Check if file already exists
                if blob.exists():
                    print(f"Skipping existing file: {gcs_path}")
                    continue
                
                try:
                    blob.upload_from_filename(str(file_path))
                    uploaded_count += 1
                    if uploaded_count % 10 == 0:
                        print(f"Uploaded {uploaded_count} files...")
                except Exception as e:
                    print(f"Error uploading {file_path}: {e}")
        
        print(f"Uploaded {uploaded_count} files from {local_dir} to gs://{self.bucket_name}/{gcs_prefix}")
        return uploaded_count
    
    def create_dataset_manifest(self, dataset_name: str) -> str:
        """
        Create a dataset manifest file for Vertex AI.
        
        Args:
            dataset_name: Name for the dataset
            
        Returns:
            GCS path to the manifest file
        """
        labels = self.get_class_labels(".")
        
        # Create manifest entries
        manifest_lines = []
        
        for split in ["train", "test", "val"]:
            split_dir = Path(split)
            if not split_dir.exists():
                continue
                
            for label in labels:
                label_dir = split_dir / label
                if not label_dir.exists():
                    continue
                    
                for image_file in label_dir.glob("*"):
                    if image_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
                        gcs_path = f"gs://{self.bucket_name}/{dataset_name}/{split}/{label}/{image_file.name}"
                        manifest_lines.append(f"{gcs_path},{label}")
        
        # Save manifest locally
        manifest_path = f"{dataset_name}_manifest.csv"
        with open(manifest_path, 'w') as f:
            f.write("image_path,label\n")
            f.write("\n".join(manifest_lines))
        
        # Upload manifest to GCS
        bucket = self.storage_client.bucket(self.bucket_name)
        manifest_blob = bucket.blob(f"{dataset_name}/manifest.csv")
        manifest_blob.upload_from_filename(manifest_path)
        
        manifest_gcs_path = f"gs://{self.bucket_name}/{dataset_name}/manifest.csv"
        print(f"Created manifest file: {manifest_gcs_path}")
        
        return manifest_gcs_path
    
    def upload_dataset(self, dataset_name: str) -> Tuple[str, List[str]]:
        """
        Upload the complete dataset (train/test/val) to GCS.
        
        Args:
            dataset_name: Name for the dataset
            
        Returns:
            Tuple of (manifest_gcs_path, class_labels)
        """
        print(f"Starting upload of dataset: {dataset_name}")
        
        # Create bucket if needed
        self.create_bucket_if_not_exists()
        
        # Upload each split
        total_uploaded = 0
        for split in ["train", "test", "val"]:
            if os.path.exists(split):
                count = self.upload_directory_to_gcs(split, f"{dataset_name}/{split}")
                total_uploaded += count
        
        print(f"Total files uploaded: {total_uploaded}")
        
        # Create and upload manifest
        manifest_path = self.create_dataset_manifest(dataset_name)
        labels = self.get_class_labels(".")
        
        return manifest_path, labels


def main():
    parser = argparse.ArgumentParser(description="Upload dataset to Vertex AI")
    parser.add_argument("--project-id", required=True, help="Google Cloud project ID")
    parser.add_argument("--region", default="us-central1", help="Google Cloud region")
    parser.add_argument("--bucket-name", required=True, help="GCS bucket name")
    parser.add_argument("--dataset-name", default="plant-disease-dataset", help="Dataset name")
    parser.add_argument("--credentials", help="Path to service account JSON file")
    
    args = parser.parse_args()
    
    try:
        uploader = VertexAIDatasetUploader(
            project_id=args.project_id,
            region=args.region,
            bucket_name=args.bucket_name,
            credentials_path=args.credentials
        )
        
        manifest_path, labels = uploader.upload_dataset(args.dataset_name)
        
        print("\n" + "="*50)
        print("UPLOAD COMPLETE!")
        print("="*50)
        print(f"Dataset uploaded to: gs://{args.bucket_name}/{args.dataset_name}")
        print(f"Manifest file: {manifest_path}")
        print(f"Number of classes: {len(labels)}")
        print(f"Classes: {', '.join(labels)}")
        print("\nNext steps:")
        print("1. Go to Vertex AI console")
        print("2. Create a new dataset using the manifest file")
        print("3. Start training your model")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
