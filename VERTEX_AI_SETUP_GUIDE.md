# Vertex AI Dataset Upload Guide

This guide will help you upload your train/test/val folders to Google Cloud Storage and prepare them for Vertex AI model training.

## Prerequisites

1. **Google Cloud Project**: You need a Google Cloud project with billing enabled
2. **Vertex AI API**: Enable the Vertex AI API in your project
3. **Cloud Storage API**: Enable the Cloud Storage API in your project
4. **Authentication**: Set up authentication (service account or gcloud CLI)

## Step 1: Set Up Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the required APIs:
   - Vertex AI API
   - Cloud Storage API
   - Compute Engine API (if needed)

## Step 2: Set Up Authentication

### Option A: Service Account (Recommended for scripts)

1. Go to IAM & Admin > Service Accounts in Google Cloud Console
2. Create a new service account
3. Grant the following roles:
   - Storage Admin
   - Vertex AI User
   - AI Platform Admin
4. Create and download a JSON key file
5. Save the JSON file securely (e.g., as `service-account-key.json`)

### Option B: gcloud CLI

1. Install Google Cloud CLI from [here](https://cloud.google.com/sdk/docs/install)
2. Run: `gcloud auth login`
3. Run: `gcloud config set project YOUR_PROJECT_ID`

## Step 3: Install Python Dependencies

```bash
pip install -r requirements_vertex_ai.txt
```

## Step 4: Upload Your Dataset

### Using the Python Script

```bash
python upload_to_vertex_ai.py \
    --project-id YOUR_PROJECT_ID \
    --bucket-name YOUR_BUCKET_NAME \
    --dataset-name plant-disease-dataset \
    --region us-central1 \
    --credentials path/to/service-account-key.json
```

### Parameters Explanation

- `--project-id`: Your Google Cloud project ID
- `--bucket-name`: Name for the GCS bucket (will be created if doesn't exist)
- `--dataset-name`: Name for your dataset (default: plant-disease-dataset)
- `--region`: Google Cloud region (default: us-central1)
- `--credentials`: Path to service account JSON file (optional if using gcloud auth)

### Example Command

```bash
python upload_to_vertex_ai.py \
    --project-id my-ml-project-123 \
    --bucket-name my-plant-disease-bucket \
    --dataset-name olive-palm-disease-v1 \
    --region us-central1 \
    --credentials ./service-account-key.json
```

## Step 5: Verify Upload

After running the script, you should see:
1. Files uploaded to Google Cloud Storage
2. A manifest file created
3. Summary of uploaded classes and file counts

You can verify in the Google Cloud Console:
- Go to Cloud Storage and check your bucket
- Files should be organized as: `dataset-name/train/class-name/images`

## Step 6: Create Vertex AI Dataset

### Option A: Using Google Cloud Console

1. Go to Vertex AI > Datasets in Google Cloud Console
2. Click "Create Dataset"
3. Choose "Image Classification"
4. Select "Single-label classification"
5. Import data from Cloud Storage using the manifest file
6. The manifest file path will be: `gs://YOUR_BUCKET_NAME/DATASET_NAME/manifest.csv`

### Option B: Using Python API (Advanced)

The script can be extended to automatically create the Vertex AI dataset. Let me know if you need this functionality.

## Step 7: Train Your Model

1. Once the dataset is created, go to Vertex AI > Training
2. Create a new training job
3. Select your dataset
4. Choose AutoML Image Classification
5. Configure training parameters
6. Start training

## Troubleshooting

### Common Issues

1. **Authentication Error**: Make sure your service account has the right permissions
2. **Bucket Already Exists**: Choose a unique bucket name
3. **API Not Enabled**: Enable Vertex AI and Cloud Storage APIs
4. **Quota Exceeded**: Check your project quotas and billing

### File Structure Expected

Your current structure is perfect:
```
Disease_prime/
├── train/
│   ├── olive_olive_acarien/
│   ├── olive_olive_anthracanose/
│   ├── palm_black_scorch/
│   └── ...
├── test/
│   ├── olive_olive_acarien/
│   └── ...
└── val/
    ├── olive_olive_acarien/
    └── ...
```

## Cost Considerations

- **Cloud Storage**: ~$0.02 per GB per month
- **Vertex AI Training**: Varies by model size and training time
- **Vertex AI Prediction**: Pay per prediction or hourly for endpoints

## Next Steps After Upload

1. Monitor the upload progress
2. Verify data in Cloud Storage
3. Create Vertex AI dataset
4. Start model training
5. Evaluate model performance
6. Deploy model for predictions

## Support

If you encounter issues:
1. Check the Google Cloud Console for error messages
2. Verify your authentication and permissions
3. Check the script output for detailed error information
