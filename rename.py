import os

def normalize_folder_name(name):
    """Lowercase, replace spaces with underscores"""
    return name.strip().lower().replace(" ", "_")

def rename_class_folders(dataset_path, culture_prefix):
    """
    Renames folders in train/val/test to format: prefix_normalized_classname
    """
    for split in ['train', 'val', 'test']:
        split_dir = os.path.join(dataset_path, split)
        if not os.path.exists(split_dir):
            print(f"❌ Directory does not exist: {split_dir}")
            continue
        
        for class_folder in os.listdir(split_dir):
            old_path = os.path.join(split_dir, class_folder)
            if not os.path.isdir(old_path):
                continue

            new_folder_name = f"{culture_prefix}_{normalize_folder_name(class_folder)}"
            new_path = os.path.join(split_dir, new_folder_name)

            if new_path != old_path:
                print(f"🔄 Renaming: {old_path} → {new_path}")
                os.rename(old_path, new_path)
            else:
                print(f"✅ Already normalized: {old_path}")

# === Modify these paths below to match your folders ===
olive_dataset_path = r"C:\Users\<USER>\Desktop\olive_dataset"
palm_dataset_path = r"C:\Users\<USER>\Desktop\Disease_prime"

# === Run for both datasets ===
rename_class_folders(olive_dataset_path, "olive")
rename_class_folders(palm_dataset_path, "palm")
