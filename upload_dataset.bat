@echo off
echo ========================================
echo Vertex AI Dataset Upload Script
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Install required packages
echo Installing required Python packages...
pip install -r requirements_vertex_ai.txt

echo.
echo ========================================
echo Configuration Required
echo ========================================
echo.
echo Before running this script, you need:
echo 1. A Google Cloud project with billing enabled
echo 2. Vertex AI and Cloud Storage APIs enabled
echo 3. A service account JSON key file
echo.

set /p PROJECT_ID="Enter your Google Cloud Project ID: "
set /p BUCKET_NAME="Enter your GCS bucket name (will be created if doesn't exist): "
set /p DATASET_NAME="Enter dataset name (default: plant-disease-dataset): "
set /p CREDENTIALS_PATH="Enter path to service account JSON file: "

REM Set default dataset name if empty
if "%DATASET_NAME%"=="" set DATASET_NAME=plant-disease-dataset

echo.
echo ========================================
echo Starting Upload
echo ========================================
echo Project ID: %PROJECT_ID%
echo Bucket Name: %BUCKET_NAME%
echo Dataset Name: %DATASET_NAME%
echo Credentials: %CREDENTIALS_PATH%
echo.

REM Run the upload script
python upload_to_vertex_ai.py --project-id "%PROJECT_ID%" --bucket-name "%BUCKET_NAME%" --dataset-name "%DATASET_NAME%" --credentials "%CREDENTIALS_PATH%"

echo.
echo ========================================
echo Upload Complete
echo ========================================
echo.
echo Next steps:
echo 1. Go to Google Cloud Console
echo 2. Navigate to Vertex AI ^> Datasets
echo 3. Create a new dataset using the uploaded manifest file
echo 4. Start training your model
echo.
pause
