import streamlit as st
from PIL import Image
from ultralytics import YOLO
import torch # Good to have if ultralytics needs it explicitly in some environments
import os
import pandas as pd
from datetime import datetime
import matplotlib.pyplot as plt # Kept for potential future custom charts
import plotly.express as px
import plotly.graph_objects as go # For more complex Plotly charts later if needed


# --- Configuration ---
MODEL_PATH = "runs/classify/train3/weights/best.pt" # Ensure this path is correct
IMAGE_SAVE_DIR = "uploaded_images_dashboard"
PREDICTIONS_LOG_FILE = "predictions_log.csv"

# This list is used if model.names is not available or not complete,
# primarily for the sidebar info and ensuring structured logging has a known name.
FALLBACK_CLASS_NAMES = [
    "olive_acarien", "olive_anthracanose", "olive_carence_en_azote", "olive_carence_en_bore",
    "olive_carence_en_calcium", "olive_carence_en_magnesium", "olive_carence_en_phosphore",
    "olive_carence_en_potassium", "olive_cochenille_noir", "olive_hylesine", "olive_mouche_de_olivier",
    "olive_neiroun", "olive_oeil_de_paon", "olive_in_good_condition", "olive_psylle",
    "olive_pyrale", "olive_teigne", "olive_tuberculose", "palm_black_scorch", "palm_brown_spots",
    "palm_bug", "palm_dubas", "palm_fusarium_wilt", "palm_healthy", "palm_honey",
    "palm_leaf_spots", "palm_magnesium_deficiency", "palm_manganese_deficiency",
    "palm_potassium_deficiency", "palm_rachis_blight", "palm_white_scale"
]

# --- NEW: Complete Disease Information Dictionary ---
# This dictionary stores descriptive information for each class.
DISEASE_INFO = {
    # === OLIVE DISEASES ===
    "olive_acarien": {
        "introduction": """
        Acarien (mites) are tiny arthropods that feed on olive leaves, causing stippling, yellowing, and bronzing of foliage. Heavy infestations can lead to defoliation and reduced photosynthesis.
        """,
        "treatment": """
        **1. Miticides:** Apply specific acaricides like abamectin or spiromesifen during active mite periods.
        **2. Biological Control:** Encourage predatory mites and insects that feed on pest mites.
        **3. Cultural Practices:** Maintain proper irrigation and avoid water stress, which makes trees more susceptible.
        """
    },
    "olive_anthracanose": {
        "introduction": """
        Anthracnose is a fungal disease caused by *Colletotrichum* species that affects olive fruits, leaves, and twigs, causing dark lesions and fruit rot, especially in humid conditions.
        """,
        "treatment": """
        **1. Fungicides:** Apply copper-based fungicides or systemic fungicides like azoxystrobin before and during wet periods.
        **2. Sanitation:** Remove and destroy infected plant debris and mummified fruits.
        **3. Pruning:** Improve air circulation through proper pruning to reduce humidity around the canopy.
        """
    },
    "olive_carence_en_azote": {
        "introduction": """
        Nitrogen deficiency (Carence en Azote) appears as general yellowing of older leaves, reduced growth, and smaller fruit size. It's one of the most common nutritional deficiencies in olive trees.
        """,
        "treatment": """
        **1. Nitrogen Fertilization:** Apply nitrogen-rich fertilizers like urea or ammonium nitrate in early spring.
        **2. Organic Matter:** Incorporate compost or well-aged manure to provide slow-release nitrogen.
        **3. Foliar Application:** For quick correction, apply foliar nitrogen sprays during active growth periods.
        """
    },
    "olive_carence_en_bore": {
        "introduction": """
        Boron deficiency (Carence en Bore) causes shoot dieback, poor fruit set, and internal fruit breakdown. Olive trees are particularly sensitive to boron levels in soil.
        """,
        "treatment": """
        **1. Boron Application:** Apply borax or boric acid to soil, but be very careful with rates as boron can be toxic in excess.
        **2. Foliar Spray:** Apply dilute boron solutions as foliar sprays during flowering and fruit development.
        **3. Soil Testing:** Regular soil testing to monitor boron levels and avoid over-application.
        """
    },
    "olive_carence_en_calcium": {
        "introduction": """
        Calcium deficiency (Carence en Calcium) leads to poor fruit quality, increased susceptibility to diseases, and potential fruit cracking. It often occurs in acidic soils.
        """,
        "treatment": """
        **1. Lime Application:** Apply agricultural lime to raise soil pH and increase calcium availability.
        **2. Calcium Fertilizers:** Use calcium chloride or calcium nitrate for direct calcium supplementation.
        **3. Soil Management:** Improve soil structure and drainage to enhance calcium uptake.
        """
    },
    "olive_carence_en_magnesium": {
        "introduction": """
        Magnesium deficiency (Carence en Magnesium) appears as interveinal chlorosis on older leaves, with yellowing between leaf veins while veins remain green.
        """,
        "treatment": """
        **1. Magnesium Sulfate:** Apply Epsom salts (magnesium sulfate) to soil around the root zone.
        **2. Foliar Application:** Spray magnesium sulfate solution on leaves for faster uptake.
        **3. Dolomitic Lime:** Use dolomitic limestone to provide both calcium and magnesium.
        """
    },
    "olive_carence_en_phosphore": {
        "introduction": """
        Phosphorus deficiency (Carence en Phosphore) causes stunted growth, delayed maturity, and purplish discoloration of leaves, particularly affecting root development and flowering.
        """,
        "treatment": """
        **1. Phosphate Fertilizers:** Apply superphosphate or rock phosphate to soil before planting or during dormant season.
        **2. Mycorrhizal Inoculation:** Use mycorrhizal fungi to improve phosphorus uptake efficiency.
        **3. Organic Phosphorus:** Apply bone meal or compost rich in phosphorus.
        """
    },
    "olive_carence_en_potassium": {
        "introduction": """
        Potassium deficiency (Carence en Potassium) manifests as marginal leaf burn, reduced fruit quality, and increased susceptibility to cold damage and diseases.
        """,
        "treatment": """
        **1. Potassium Fertilizers:** Apply potassium sulfate or potassium chloride, preferably in split applications.
        **2. Foliar Feeding:** Use potassium nitrate foliar sprays during fruit development.
        **3. Organic Sources:** Apply wood ash or potassium-rich compost materials.
        """
    },
    "olive_cochenille_noir": {
        "introduction": """
        Black scale (Cochenille Noir) are sap-sucking insects that attach to olive branches and leaves, producing honeydew that leads to sooty mold and weakening the tree.
        """,
        "treatment": """
        **1. Horticultural Oils:** Apply mineral or neem oil to suffocate the scales, especially effective on young crawlers.
        **2. Systemic Insecticides:** Use imidacloprid or other systemic insecticides for severe infestations.
        **3. Biological Control:** Encourage natural predators like ladybugs and parasitic wasps.
        """
    },
    "olive_hylesine": {
        "introduction": """
        Hylesine (bark beetles) bore into olive tree bark and wood, creating galleries that can girdle branches and cause dieback, particularly affecting stressed trees.
        """,
        "treatment": """
        **1. Tree Health:** Maintain tree vigor through proper irrigation and fertilization to resist attack.
        **2. Pruning:** Remove and destroy infested branches immediately to prevent spread.
        **3. Preventive Sprays:** Apply preventive insecticide treatments during beetle flight periods.
        """
    },
    "olive_mouche_de_olivier": {
        "introduction": """
        Olive fruit fly (Mouche de l'Olivier) is the most serious pest of olives, with larvae developing inside the fruit, causing fruit drop and quality degradation.
        """,
        "treatment": """
        **1. Protein Baits:** Use protein-based baits with insecticides to attract and kill adult flies.
        **2. Mass Trapping:** Deploy pheromone and food-based traps to monitor and reduce populations.
        **3. Kaolin Clay:** Apply kaolin clay sprays to create a barrier that deters egg-laying.
        """
    },
    "olive_neiroun": {
        "introduction": """
        Neiroun is a physiological disorder causing fruit to turn black and shrivel before maturity, often related to water stress, nutritional imbalances, or extreme temperatures.
        """,
        "treatment": """
        **1. Water Management:** Ensure consistent irrigation, especially during fruit development periods.
        **2. Nutrition:** Maintain balanced fertilization, particularly adequate potassium and calcium.
        **3. Stress Reduction:** Provide shade during extreme heat and protect from temperature fluctuations.
        """
    },
    "olive_oeil_de_paon": {
        "introduction": """
        Peacock spot (Oeil de Paon) is a fungal disease caused by *Spilocaea oleagina* that creates characteristic circular spots with yellow halos on leaves, leading to defoliation.
        """,
        "treatment": """
        **1. Copper Fungicides:** Apply copper-based fungicides preventively, especially before rainy periods.
        **2. Pruning:** Improve air circulation through proper pruning to reduce leaf wetness duration.
        **3. Sanitation:** Remove fallen leaves and infected plant material to reduce inoculum sources.
        """
    },
    "olive_in_good_condition": {
        "introduction": """
        The olive tree appears to be in excellent health with vibrant foliage, proper fruit development, and no visible signs of diseases, pests, or nutritional deficiencies.
        """,
        "treatment": """
        Maintain current good practices:
        - Continue regular irrigation and balanced fertilization schedule.
        - Monitor regularly for early signs of pests or diseases.
        - Maintain proper pruning for good air circulation and light penetration.
        """
    },
    "olive_psylle": {
        "introduction": """
        Olive psyllid (Psylle) are small jumping insects that feed on olive leaves and shoots, causing yellowing, stunting, and honeydew production that leads to sooty mold.
        """,
        "treatment": """
        **1. Insecticidal Soaps:** Apply insecticidal soap or neem oil to control nymphs and adults.
        **2. Systemic Insecticides:** Use imidacloprid for severe infestations, applied as soil drench.
        **3. Natural Predators:** Encourage beneficial insects like lacewings and predatory mites.
        """
    },
    "olive_pyrale": {
        "introduction": """
        Olive moth (Pyrale) larvae feed on olive flowers, young fruits, and shoots, causing significant damage to fruit production and shoot growth.
        """,
        "treatment": """
        **1. Pheromone Traps:** Use sex pheromone traps to monitor and mass-trap adult moths.
        **2. Bacillus thuringiensis:** Apply Bt sprays targeting young larvae when they are most susceptible.
        **3. Timing:** Time treatments based on moth flight periods and egg-laying activity.
        """
    },
    "olive_teigne": {
        "introduction": """
        Olive kernel borer (Teigne) larvae tunnel into olive pits, destroying the kernel and causing premature fruit drop, significantly affecting oil quality and yield.
        """,
        "treatment": """
        **1. Pheromone Monitoring:** Use pheromone traps to determine optimal timing for treatments.
        **2. Insecticide Applications:** Apply targeted insecticides during egg-laying periods.
        **3. Sanitation:** Remove and destroy fallen fruits to break the pest's life cycle.
        """
    },
    "olive_tuberculose": {
        "introduction": """
        Olive knot (Tuberculose) is a bacterial disease caused by *Pseudomonas savastanoi* that forms characteristic galls or knots on branches, affecting tree vigor and productivity.
        """,
        "treatment": """
        **1. Pruning:** Remove and destroy infected branches, cutting well below visible symptoms.
        **2. Copper Treatments:** Apply copper-based bactericides, especially after pruning and before rainy periods.
        **3. Sanitation:** Disinfect pruning tools between cuts to prevent disease spread.
        """
    },

    # === PALM DISEASES ===
    "palm_black_scorch": {
        "introduction": """
        Black scorch, caused by the fungus *Thielaviopsis paradoxa*, manifests as a severe blight on leaf buds, trunks, and fruits. It can lead to heart rot and significant yield loss if not managed.
        """,
        "treatment": """
        **1. Pruning:** Surgically remove and destroy all infected plant parts to prevent the spread of spores.
        **2. Fungicides:** Apply copper-based or systemic fungicides like Thiophanate-methyl to the pruned areas and as a preventative spray.
        **3. Sanitation:** Maintain orchard hygiene by removing fallen debris.
        """
    },
    "palm_brown_spots": {
        "introduction": """
        'Brown spots' is a general term for a variety of fungal or bacterial infections that cause necrotic (dead) spots on palm fronds. While often minor, they can indicate underlying health issues or poor environmental conditions.
        """,
        "treatment": """
        **1. Improve Air Circulation:** Prune dense foliage to increase airflow, which helps keep leaves dry.
        **2. Water Management:** Avoid overhead irrigation that wets the foliage; water at the base of the palm instead.
        **3. Fungicides:** If the problem is severe, apply a broad-spectrum copper-based fungicide.
        """
    },
    "palm_bug": {
        "introduction": """
        This is a general category for damage caused by unidentified chewing or sucking insects. Symptoms can include holes in leaves, chewed edges, discoloration, or stunted growth.
        """,
        "treatment": """
        **1. Identify the Pest:** First, try to identify the specific insect causing the damage. Look for the bugs themselves, their eggs, or larvae.
        **2. General Control:** For many common pests, spraying with insecticidal soap or neem oil can be effective and is environmentally friendly.
        **3. Manual Removal:** For larger insects like beetles, manual removal can significantly reduce their numbers.
        """
    },
    "palm_dubas": {
        "introduction": """
        The Dubas bug (*Ommatissus lybicus*) is a sap-sucking insect that is a major pest for date palms. Infestations lead to reduced yield and the production of "honeydew" (a sugary waste), which promotes the growth of sooty mold.
        """,
        "treatment": """
        **1. Systemic Insecticides:** Applying systemic insecticides (e.g., imidacloprid) to the soil or trunk is the most effective control method.
        **2. Contact Sprays:** During high infestation, contact insecticides can be sprayed, but timing is crucial to target the pest's life cycle.
        **3. Natural Predators:** Encourage natural predators like lacewings and ladybugs.
        """
    },
    "palm_fusarium_wilt": {
        "introduction": """
        Also known as Bayoud disease, Fusarium wilt is a lethal fungal disease caused by *Fusarium oxysporum*. It blocks the palm's vascular system, causing fronds on one side to wilt and die, eventually killing the entire tree.
        """,
        "treatment": """
        **1. No Cure:** There is no effective chemical cure for an infected palm.
        **2. Removal:** Infected palms must be uprooted and burned immediately to prevent soil contamination and spread to other palms.
        **3. Prevention:** Plant resistant cultivars and avoid waterlogging. Do not replant in the same spot without proper soil fumigation.
        """
    },
    "palm_healthy": {
        "introduction": """
        The palm appears to be in good health. The foliage is green and vibrant, with no visible signs of nutrient deficiencies, pests, or common diseases.
        """,
        "treatment": """
        Maintain good agricultural practices:
        - Ensure proper irrigation and drainage.
        - Follow a balanced fertilization schedule.
        - Regularly monitor for early signs of pests or diseases.
        """
    },
    "palm_honey": {
        "introduction": """
        This refers to "honeydew," a sugary, sticky substance excreted by sap-sucking insects like aphids, mealybugs, and scale. This substance often leads to the growth of a black fungus called sooty mold.
        """,
        "treatment": """
        **1. Control the Pest:** The primary treatment is to eliminate the insect pest that is producing the honeydew. Identify the pest and use horticultural oils, insecticidal soap, or appropriate insecticides.
        **2. Wash the Leaves:** Once the pests are gone, the honeydew and sooty mold can be washed off with a strong spray of water, sometimes mixed with a little mild soap.
        """
    },
    "palm_leaf_spots": {
        "introduction": """
        Leaf spots are caused by specific fungi (like *Pestalotiopsis*) that result in distinct discolored, often circular lesions on the palm fronds. They can have a yellow halo and may merge to form larger blighted areas.
        """,
        "treatment": """
        **1. Sanitation:** Remove and destroy heavily infected fronds.
        **2. Fungicides:** Apply copper-based or broad-spectrum fungicides, ensuring good coverage of the foliage.
        **3. Improve Plant Health:** Ensure proper fertilization and watering to help the palm resist infection.
        """
    },
    "palm_magnesium_deficiency": {
        "introduction": """
        Magnesium (Mg) deficiency appears as distinct yellow-orange bands along the margins of **older** leaves, while the central part of the frond remains green. It is common in sandy or highly alkaline soils.
        """,
        "treatment": """
        **1. Fertilization:** Apply magnesium sulfate (Epsom salts) or kieserite to the soil around the palm's root zone. This is a long-term solution.
        **2. Foliar Spray:** For a quicker but temporary fix, a foliar spray of a water-soluble magnesium sulfate solution can be used.
        """
    },
    "palm_manganese_deficiency": {
        "introduction": """
        Manganese (Mn) deficiency is often called "frizzle top." Symptoms appear on **new** emerging leaves, which look chlorotic (yellow), weak, and smaller than normal, with a characteristic scorched or "frizzled" appearance.
        """,
        "treatment": """
        **1. Soil Application:** Apply manganese sulfate to the soil. Be careful with the application rate, as too much can be toxic.
        **2. Foliar Spray:** A foliar spray of manganese sulfate will provide a much faster green-up of the affected fronds.
        """
    },
    "palm_potassium_deficiency": {
        "introduction": """
        Potassium (K) deficiency is one of the most common nutritional problems. Symptoms appear on the **oldest** leaves as translucent yellow or orange spotting. These spots can enlarge, leading to necrosis (tissue death) at the tips and margins of the fronds.
        """,
        "treatment": """
        **1. Slow-Release Fertilizer:** Apply a controlled-release potassium sulfate fertilizer to the soil. Correction is very slow and may take a year or more to show improvement.
        **2. Balanced Nutrients:** Ensure the fertilizer also contains magnesium to prevent nutrient imbalances, as high potassium can induce a magnesium deficiency.
        """
    },
    "palm_rachis_blight": {
        "introduction": """
        This disease affects the rachis (the main stem of the frond to which leaflets are attached). It is often caused by fungi and leads to discoloration, decay, and eventual death of the entire frond.
        """,
        "treatment": """
        **1. Pruning:** Remove and destroy infected fronds to reduce the amount of fungal inoculum.
        **2. Fungicides:** Application of broad-spectrum or copper-based fungicides may help protect healthy fronds.
        **3. Cultural Practices:** Avoid overhead watering and ensure good air circulation around the palm.
        """
    },
    "palm_white_scale": {
        "introduction": """
        White scale are small, immobile, sap-sucking insects that cover themselves with a waxy, white coating. They appear as small white specks on the fronds and can cause yellowing and dieback if the infestation is severe.
        """,
        "treatment": """
        **1. Horticultural Oils:** Spraying with horticultural oil is very effective as it suffocates the insects. This is a preferred method for controlling scale.
        **2. Systemic Insecticides:** For heavy infestations, a soil-applied systemic insecticide can be used.
        **3. Manual Removal:** On smaller plants, scale can be wiped off with a cloth or cotton swab dipped in rubbing alcohol.
        """
    }
}


# --- Helper Functions ---
def normalize_class_name(class_name):
    """Normalizes class names to remove double prefixes and ensure consistent format."""
    # Handle double olive prefix: olive_olive_xxx -> olive_xxx
    if class_name.startswith("olive_olive_"):
        return class_name.replace("olive_olive_", "olive_", 1)
    # Handle double palm prefix: palm_palm_xxx -> palm_xxx
    elif class_name.startswith("palm_palm_"):
        return class_name.replace("palm_palm_", "palm_", 1)
    else:
        return class_name

def get_plant_type(class_name):
    """Determines if the predicted class is for olive or palm plant."""
    normalized_name = normalize_class_name(class_name)
    if normalized_name.startswith("olive_"):
        return "Olive"
    elif normalized_name.startswith("palm_"):
        return "Palm"
    else:
        return "Unknown"

def get_disease_name_without_prefix(class_name):
    """Removes the plant type prefix from the class name for display."""
    normalized_name = normalize_class_name(class_name)
    if normalized_name.startswith("olive_"):
        return normalized_name[6:]  # Remove "olive_" prefix
    elif normalized_name.startswith("palm_"):
        return normalized_name[5:]   # Remove "palm_" prefix
    else:
        return normalized_name

def get_disease_info(class_name):
    """Gets disease information, handling both normalized and non-normalized class names."""
    normalized_name = normalize_class_name(class_name)
    return DISEASE_INFO.get(normalized_name, None)

def initialize_app():
    """Initializes necessary directories and session state variables."""
    if not os.path.exists(IMAGE_SAVE_DIR):
        os.makedirs(IMAGE_SAVE_DIR)
    
    if 'model_class_names' not in st.session_state:
        st.session_state.model_class_names = None
    
    if 'current_page' not in st.session_state:
        st.session_state.current_page = "Classifier"

@st.cache_resource # Caches the model loading
def load_yolo_model(model_path):
    """Loads the YOLOv8 classification model."""
    if not os.path.exists(model_path):
        return None
    try:
        model = YOLO(model_path)
        if hasattr(model, 'names') and isinstance(model.names, dict) and model.names:
            st.session_state.model_class_names = model.names
        else:
            st.session_state.model_class_names = None 
        return model
    except Exception as e:
        print(f"Error loading model: {e}") 
        return None

def get_effective_class_names_map():
    """Determines the class name mapping to use (model's or fallback)."""
    model_native_names = st.session_state.get('model_class_names', None)
    if model_native_names:
        # Normalize the model class names to handle double prefixes
        normalized_names = {k: normalize_class_name(v) for k, v in model_native_names.items()}
        return normalized_names, "Using class names from model (`model.names`) - normalized."
    else:
        return {i: name for i, name in enumerate(FALLBACK_CLASS_NAMES)}, \
               "`model.names` not found or invalid. Using `FALLBACK_CLASS_NAMES`."

def predict_image_and_get_structured_data(model, image_input):
    """
    Performs inference and returns verbose string, speed, and structured top-1 prediction.
    """
    if model is None:
        return None, None, None, None

    results = model(image_input)
    
    prediction_summary_string = None
    speed_info = None
    top1_class_name = None
    top1_confidence = None

    if results and results[0].probs is not None: 
        prediction_summary_string = results[0].verbose()
        speed_info = results[0].speed
        
        class_names_map, _ = get_effective_class_names_map()
        
        top1_idx = results[0].probs.top1
        top1_confidence = results[0].probs.top1conf.item()
        raw_class_name = class_names_map.get(top1_idx, f"Unknown Index {top1_idx}")
        top1_class_name = normalize_class_name(raw_class_name)  # Normalize the class name

        return prediction_summary_string, speed_info, top1_class_name, top1_confidence
    else:
        st.warning("Model did not return valid results or probabilities.")
        return None, None, None, None

def log_prediction_data(image_filename, predicted_disease, confidence):
    """Appends prediction data to the CSV log file."""
    plant_type = get_plant_type(predicted_disease)
    disease_name = get_disease_name_without_prefix(predicted_disease)

    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "image_filename": image_filename,
        "plant_type": plant_type,
        "predicted_disease": predicted_disease,
        "disease_name": disease_name,
        "confidence": confidence
    }
    df_entry = pd.DataFrame([log_entry])
    
    file_exists = os.path.isfile(PREDICTIONS_LOG_FILE)
    try:
        df_entry.to_csv(PREDICTIONS_LOG_FILE, mode='a', header=not file_exists, index=False)
    except Exception as e:
        st.error(f"Failed to write to log file: {e}")


# --- UI Rendering Functions ---
def render_classifier_page(model):
    st.header("� Plant Disease Classifier")
    st.markdown("Upload an image to classify olive or palm plant diseases.")

    uploaded_file = st.file_uploader("Choose an image of an olive or palm plant...", type=["jpg", "jpeg", "png"])

    if uploaded_file is not None:
        if model is None:
            st.error("Model could not be loaded. Please check the `MODEL_PATH` in the script and ensure the file exists.")
            return 

        image = Image.open(uploaded_file)
        
        col1, col2 = st.columns(2)
        with col1:
            st.image(image, caption="Uploaded Image", use_column_width=True)

        with col2:
            if st.button("🔍 Classify Plant Disease", use_container_width=True):
                with st.spinner("🕵️ Analyzing image..."):
                    summary_str, speed, top_class, top_conf = \
                        predict_image_and_get_structured_data(model, image)

                if summary_str and top_class is not None and top_conf is not None:
                    # --- Display primary result ---
                    plant_type = get_plant_type(top_class)
                    disease_name = get_disease_name_without_prefix(top_class)

                    # Display plant type first
                    if plant_type != "Unknown":
                        st.success(f"🌱 Plant Type: **{plant_type}**")

                    # Display disease prediction
                    st.subheader(f"🔍 Disease: {disease_name.replace('_', ' ').title()}")
                    st.progress(top_conf, text=f"Confidence: {top_conf:.2%}")

                    # --- MODIFIED: Display Disease Information using Expander ---
                    disease_info = get_disease_info(top_class)
                    if disease_info:
                        with st.expander(f"ℹ️ Learn more about {top_class}", expanded=True):
                            st.markdown(f"### Introduction\n{disease_info['introduction']}")
                            st.markdown("---")
                            st.markdown(f"### Recommended Treatment\n{disease_info['treatment']}")
                    elif "Unknown Index" not in top_class:
                        st.info("No detailed information available for this prediction in the app's database.")
                    # --- End of Modification ---
                    
                    st.markdown("---") # Visual separator

                    # --- Display technical details ---
                    with st.expander("Show Technical Details"):
                        st.subheader("🔎 Model Prediction Summary:")
                        st.code(summary_str) 

                        if speed:
                            st.markdown("**Processing Speed:**")
                            st.text(f"Pre-process: {speed.get('preprocess', 'N/A'):.1f}ms\n"
                                    f"Inference: {speed.get('inference', 'N/A'):.1f}ms\n"
                                    f"Post-process: {speed.get('postprocess', 'N/A'):.1f}ms")
                    
                    try:
                        timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                        safe_original_filename = "".join(c for c in uploaded_file.name if c.isalnum() or c in ('.', '_')).rstrip()
                        if not safe_original_filename: 
                            safe_original_filename = "uploaded_image" 
                        unique_image_filename = f"{timestamp_str}_{safe_original_filename}"
                        image_save_path = os.path.join(IMAGE_SAVE_DIR, unique_image_filename)
                        image.save(image_save_path)
                        
                        log_prediction_data(unique_image_filename, top_class, top_conf)
                        st.success(f"Image saved as {unique_image_filename} and prediction logged.")
                    except Exception as e:
                        st.error(f"Error saving image or logging prediction: {e}")
            
                    if "healthy" in top_class.lower() or "good_condition" in top_class.lower():
                        st.balloons()
                        
                else:
                    st.error("Could not get a valid prediction from the model.")
    else:
        st.info("Upload an image to begin classification.")

def render_dashboard_page():
    st.header("📊 Plant Disease Dashboard")
    st.markdown("Interactive statistics for olive and palm disease predictions.")

    if not os.path.exists(PREDICTIONS_LOG_FILE):
        st.info("No predictions have been logged yet. Classify some images first!")
        return

    try:
        # Try to read the CSV file
        df = pd.read_csv(PREDICTIONS_LOG_FILE)

        # Handle backward compatibility - add missing columns if they don't exist
        if 'plant_type' not in df.columns:
            df['plant_type'] = df['predicted_disease'].apply(get_plant_type)
        if 'disease_name' not in df.columns:
            df['disease_name'] = df['predicted_disease'].apply(get_disease_name_without_prefix)

        # Save the updated format back to the file for future use if columns were added
        if len(df.columns) > 4:  # If we added new columns
            df.to_csv(PREDICTIONS_LOG_FILE, index=False)

    except pd.errors.EmptyDataError:
        st.info("The prediction log is empty. Classify some images first!")
        return
    except pd.errors.ParserError as e:
        st.error("The prediction log file has an incompatible format.")
        if st.button("🔄 Reset Log File", help="This will delete the old log file and start fresh"):
            try:
                os.remove(PREDICTIONS_LOG_FILE)
                st.success("Log file reset successfully! You can now start making new predictions.")
                st.rerun()
            except Exception as delete_error:
                st.error(f"Error deleting log file: {delete_error}")
        st.info("Click the button above to reset the log file, or manually delete 'predictions_log.csv' from your project folder.")
        return
    except Exception as e:
        st.error(f"Error reading prediction log: {e}")
        return
        
    if df.empty:
        st.info("No predictions logged yet.")
        return

    st.markdown(f"Total predictions logged: **{len(df)}**")

    # Split data by plant type
    olive_df = df[df['plant_type'] == 'Olive']
    palm_df = df[df['plant_type'] == 'Palm']

    # Create tabs for different plant types
    tab1, tab2, tab3 = st.tabs(["🫒 Olive Diseases", "🌴 Palm Diseases", "📈 Overall Statistics"])

    with tab1:
        st.markdown("### Olive Disease Analysis")
        if not olive_df.empty:
            olive_counts = olive_df['disease_name'].value_counts().reset_index()
            olive_counts.columns = ['disease_name', 'count']

            col1, col2 = st.columns(2)
            with col1:
                fig_olive_bar = px.bar(olive_counts,
                                     x='disease_name',
                                     y='count',
                                     color='disease_name',
                                     title="Olive Disease Distribution",
                                     text_auto=True)
                fig_olive_bar.update_layout(xaxis_title="Disease", yaxis_title="Count", showlegend=False)
                fig_olive_bar.update_xaxes(tickangle=45)
                st.plotly_chart(fig_olive_bar, use_container_width=True)

            with col2:
                fig_olive_pie = px.pie(olive_counts,
                                     names='disease_name',
                                     values='count',
                                     title="Olive Disease Percentage")
                st.plotly_chart(fig_olive_pie, use_container_width=True)

            st.markdown(f"**Total Olive Predictions:** {len(olive_df)}")
        else:
            st.info("No olive disease predictions logged yet.")

    with tab2:
        st.markdown("### Palm Disease Analysis")
        if not palm_df.empty:
            palm_counts = palm_df['disease_name'].value_counts().reset_index()
            palm_counts.columns = ['disease_name', 'count']

            col1, col2 = st.columns(2)
            with col1:
                fig_palm_bar = px.bar(palm_counts,
                                    x='disease_name',
                                    y='count',
                                    color='disease_name',
                                    title="Palm Disease Distribution",
                                    text_auto=True)
                fig_palm_bar.update_layout(xaxis_title="Disease", yaxis_title="Count", showlegend=False)
                fig_palm_bar.update_xaxes(tickangle=45)
                st.plotly_chart(fig_palm_bar, use_container_width=True)

            with col2:
                fig_palm_pie = px.pie(palm_counts,
                                    names='disease_name',
                                    values='count',
                                    title="Palm Disease Percentage")
                st.plotly_chart(fig_palm_pie, use_container_width=True)

            st.markdown(f"**Total Palm Predictions:** {len(palm_df)}")
        else:
            st.info("No palm disease predictions logged yet.")

    with tab3:
        st.markdown("### Overall Statistics")

        # Plant type distribution
        plant_type_counts = df['plant_type'].value_counts().reset_index()
        plant_type_counts.columns = ['plant_type', 'count']

        col1, col2 = st.columns(2)
        with col1:
            fig_plant_bar = px.bar(plant_type_counts,
                                 x='plant_type',
                                 y='count',
                                 color='plant_type',
                                 title="Plant Type Distribution",
                                 text_auto=True)
            fig_plant_bar.update_layout(xaxis_title="Plant Type", yaxis_title="Count", showlegend=False)
            st.plotly_chart(fig_plant_bar, use_container_width=True)

        with col2:
            fig_plant_pie = px.pie(plant_type_counts,
                                 names='plant_type',
                                 values='count',
                                 title="Plant Type Percentage")
            st.plotly_chart(fig_plant_pie, use_container_width=True)



    st.markdown("### Predictions Over Time (Daily)")
    try:
        df_copy = df.copy() 
        df_copy['timestamp'] = pd.to_datetime(df_copy['timestamp'])
        preds_over_time = df_copy.set_index('timestamp').resample('D')['predicted_disease'].count().reset_index()
        preds_over_time.columns = ['date', 'count']

        if not preds_over_time.empty:
            fig_line = px.line(preds_over_time, 
                               x='date', 
                               y='count',
                               title="Number of Predictions Per Day",
                               markers=True,
                               labels={'date': 'Date', 'count': 'Number of Predictions'})
            fig_line.update_layout(xaxis_title="Date", yaxis_title="Number of Predictions")
            st.plotly_chart(fig_line, use_container_width=True)
        else:
            st.text("No time-series data to show.")
    except Exception as e:
        st.warning(f"Could not generate predictions over time chart: {e}")

    st.markdown("### Recent Predictions Log")
    st.dataframe(df.sort_values(by="timestamp", ascending=False).head(10), use_container_width=True)

    st.markdown("### Recently Saved Images")
    if os.path.exists(IMAGE_SAVE_DIR) and any(f.lower().endswith(('png', 'jpg', 'jpeg')) for f in os.listdir(IMAGE_SAVE_DIR)):
        try:
            logged_images_files = sorted(
                [f for f in os.listdir(IMAGE_SAVE_DIR) if f.lower().endswith(('png', 'jpg', 'jpeg'))],
                key=lambda f: os.path.getmtime(os.path.join(IMAGE_SAVE_DIR, f)),
                reverse=True
            )
            if logged_images_files:
                num_images_to_show = min(5, len(logged_images_files))
                image_paths_to_show = [os.path.join(IMAGE_SAVE_DIR, f) for f in logged_images_files[:num_images_to_show]]
                
                cols = st.columns(num_images_to_show if num_images_to_show > 0 else 1)
                for i, image_path in enumerate(image_paths_to_show):
                    cols[i].image(image_path, caption=os.path.basename(image_path), width=100)
            else:
                st.text("No compatible image files found in the image directory.")
        except Exception as e:
            st.error(f"Error displaying saved images: {e}")
    else:
        st.text("Image directory is empty or does not contain compatible images.")

# --- Main App Structure ---
st.set_page_config(page_title="Plant Disease Classifier", layout="wide", initial_sidebar_state="expanded")
initialize_app()

st.sidebar.title("� Plant Disease Classifier")
model = load_yolo_model(MODEL_PATH)

st.sidebar.markdown("---")
st.sidebar.header("Navigation")
page_options = ["Classifier", "Dashboard"]
current_page_selection = st.sidebar.radio(
    "Go to", 
    page_options, 
    index=page_options.index(st.session_state.current_page) # Use session state for stickiness
)
st.session_state.current_page = current_page_selection 
st.sidebar.markdown("---")

if model:
    st.sidebar.success(f"Model loaded: `{os.path.basename(MODEL_PATH)}`")
    effective_map, class_names_source_msg = get_effective_class_names_map()
    st.sidebar.markdown(f"**Class Names Status:** {class_names_source_msg}")
    if effective_map: # Ensure effective_map is not None before trying to show in JSON
        st.sidebar.json({"Effective Class Names For Logging": effective_map}, expanded=False)
else:
    st.sidebar.error("Model could not be loaded. Check `MODEL_PATH` and console for errors. Classifier page may not function correctly.")

# Page rendering
if st.session_state.current_page == "Classifier":
    render_classifier_page(model)
elif st.session_state.current_page == "Dashboard":
    render_dashboard_page()

st.sidebar.markdown("---")
st.sidebar.info("App for classifying olive and palm plant diseases.")